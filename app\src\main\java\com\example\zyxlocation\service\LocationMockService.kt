package com.example.zyxlocation.service

import android.app.Service
import android.content.Intent
import android.location.Location
import android.location.LocationManager
import android.os.IBinder
import android.util.Log
import kotlinx.coroutines.*

/**
 * 位置模拟服务
 * 负责管理虚拟位置的生成和分发
 */
class LocationMockService : Service() {
    
    private val TAG = "LocationMockService"
    private var serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // 当前模拟的位置
    private var currentMockLocation: Location? = null
    
    // 是否启用位置模拟
    private var isMockingEnabled = false
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "LocationMockService created")
        initializeService()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "LocationMockService started")
        
        intent?.let { handleIntent(it) }
        
        return START_STICKY // 服务被杀死后自动重启
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return null // 不支持绑定
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "LocationMockService destroyed")
        serviceScope.cancel()
    }
    
    private fun initializeService() {
        // 初始化位置模拟服务
        Log.d(TAG, "Initializing location mock service")
        
        // 检查是否有必要的权限
        checkPermissions()
        
        // 启动位置监听
        startLocationMocking()
    }
    
    private fun handleIntent(intent: Intent) {
        when (intent.action) {
            ACTION_START_MOCKING -> {
                val latitude = intent.getDoubleExtra(EXTRA_LATITUDE, 0.0)
                val longitude = intent.getDoubleExtra(EXTRA_LONGITUDE, 0.0)
                startMocking(latitude, longitude)
            }
            ACTION_STOP_MOCKING -> {
                stopMocking()
            }
            ACTION_UPDATE_LOCATION -> {
                val latitude = intent.getDoubleExtra(EXTRA_LATITUDE, 0.0)
                val longitude = intent.getDoubleExtra(EXTRA_LONGITUDE, 0.0)
                updateMockLocation(latitude, longitude)
            }
        }
    }
    
    private fun checkPermissions() {
        // 检查位置权限
        val locationManager = getSystemService(LOCATION_SERVICE) as LocationManager
        
        try {
            // 检查是否可以添加测试提供者
            if (!locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
                Log.w(TAG, "GPS provider is not enabled")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking permissions", e)
        }
    }
    
    private fun startLocationMocking() {
        serviceScope.launch {
            while (isActive && isMockingEnabled) {
                currentMockLocation?.let { location ->
                    // 广播模拟位置
                    broadcastMockLocation(location)
                }
                delay(1000) // 每秒更新一次
            }
        }
    }
    
    private fun startMocking(latitude: Double, longitude: Double) {
        Log.d(TAG, "Starting location mocking at: $latitude, $longitude")
        isMockingEnabled = true
        updateMockLocation(latitude, longitude)
    }
    
    private fun stopMocking() {
        Log.d(TAG, "Stopping location mocking")
        isMockingEnabled = false
        currentMockLocation = null
    }
    
    private fun updateMockLocation(latitude: Double, longitude: Double) {
        currentMockLocation = Location(LocationManager.GPS_PROVIDER).apply {
            this.latitude = latitude
            this.longitude = longitude
            this.accuracy = 1.0f
            this.time = System.currentTimeMillis()
            this.elapsedRealtimeNanos = System.nanoTime()
        }
        
        Log.d(TAG, "Mock location updated: $latitude, $longitude")
    }
    
    private fun broadcastMockLocation(location: Location) {
        // 发送广播通知其他组件位置已更新
        val intent = Intent(ACTION_LOCATION_UPDATED).apply {
            putExtra(EXTRA_LATITUDE, location.latitude)
            putExtra(EXTRA_LONGITUDE, location.longitude)
            putExtra(EXTRA_ACCURACY, location.accuracy)
            putExtra(EXTRA_TIME, location.time)
        }
        sendBroadcast(intent)
    }
    
    companion object {
        const val ACTION_START_MOCKING = "com.example.zyxlocation.START_MOCKING"
        const val ACTION_STOP_MOCKING = "com.example.zyxlocation.STOP_MOCKING"
        const val ACTION_UPDATE_LOCATION = "com.example.zyxlocation.UPDATE_LOCATION"
        const val ACTION_LOCATION_UPDATED = "com.example.zyxlocation.LOCATION_UPDATED"
        
        const val EXTRA_LATITUDE = "latitude"
        const val EXTRA_LONGITUDE = "longitude"
        const val EXTRA_ACCURACY = "accuracy"
        const val EXTRA_TIME = "time"
    }
}
